uuid: b855fe2d-106e-4aa6-9d2c-6b9b4e06edb2
langcode: en
status: true
dependencies:
  config:
    - flag.flag.gallery_flag
    - flag.flag.girl_flag
    - flag.flag.girl_info_flag
    - flag.flag.image_flag
    - flag.flag.video_flag
    - user.role.plus
    - user.role.subscriber
  module:
    - flag
    - rest
    - serialization
    - user
id: favorite_girls
label: 'API / GET / Favorites'
module: views
description: ''
tag: ''
base_table: flagging
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        entity_id:
          id: entity_id
          table: flagging
          field: entity_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: entity_id
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        entity_type:
          id: entity_type
          table: flagging
          field: entity_type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: entity_type
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        id:
          id: id
          table: flagging
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: id
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            subscriber: subscriber
            plus: plus
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: flagging
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        flag_id:
          id: flag_id
          table: flagging
          field: flag_id
          entity_type: flagging
          entity_field: flag_id
          plugin_id: bundle
          value:
            girl_flag: girl_flag
          expose:
            operator_limit_selection: false
            operator_list: {  }
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          plugin_id: user_current
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: serializer
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        uid:
          id: uid
          table: flagging
          field: uid
          relationship: none
          group_type: group
          admin_label: User
          entity_type: flagging
          entity_field: uid
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url.query_args
        - user
        - user.roles
      tags: {  }
  rest_export_1:
    id: rest_export_1
    display_title: Girls
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: none
        options:
          offset: 0
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            entity_id:
              alias: girl_id
              raw_output: false
            entity_type:
              alias: ''
              raw_output: false
            flag_id:
              alias: ''
              raw_output: false
            id:
              alias: ''
              raw_output: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/favorite/girls
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user
        - user.roles
      tags: {  }
  rest_export_2:
    id: rest_export_2
    display_title: Galleries
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: none
        options:
          offset: 0
      filters:
        flag_id:
          id: flag_id
          table: flagging
          field: flag_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: flag_id
          plugin_id: bundle
          operator: in
          value:
            gallery_flag: gallery_flag
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          plugin_id: user_current
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            entity_id:
              alias: media_id
              raw_output: false
            entity_type:
              alias: ''
              raw_output: false
            id:
              alias: ''
              raw_output: false
      defaults:
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/favorite/galleries
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user
        - user.roles
      tags: {  }
  rest_export_3:
    id: rest_export_3
    display_title: Images
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 1000
      filters:
        flag_id:
          id: flag_id
          table: flagging
          field: flag_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: flag_id
          plugin_id: bundle
          operator: in
          value:
            image_flag: image_flag
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          plugin_id: user_current
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            entity_id:
              alias: media_id
              raw_output: false
            entity_type:
              alias: ''
              raw_output: false
            id:
              alias: ''
              raw_output: false
      defaults:
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/favorite/images
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user
        - user.roles
      tags: {  }
  rest_export_4:
    id: rest_export_4
    display_title: 'Girl Infos'
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: none
        options:
          offset: 0
      filters:
        flag_id:
          id: flag_id
          table: flagging
          field: flag_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: flag_id
          plugin_id: bundle
          operator: in
          value:
            girl_info_flag: girl_info_flag
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          plugin_id: user_current
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            entity_id:
              alias: media_id
              raw_output: false
            entity_type:
              alias: ''
              raw_output: false
            id:
              alias: ''
              raw_output: false
      defaults:
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/favorite/girl-infos
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user
        - user.roles
      tags: {  }
  rest_export_5:
    id: rest_export_5
    display_title: Videos
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: none
        options:
          offset: 0
      filters:
        flag_id:
          id: flag_id
          table: flagging
          field: flag_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: flagging
          entity_field: flag_id
          plugin_id: bundle
          operator: in
          value:
            video_flag: video_flag
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          plugin_id: user_current
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            entity_id:
              alias: media_id
              raw_output: false
            entity_type:
              alias: ''
              raw_output: false
            id:
              alias: ''
              raw_output: false
      defaults:
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/favorite/videos
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user
        - user.roles
      tags: {  }
