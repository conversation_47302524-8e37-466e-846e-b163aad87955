uuid: fda2e99c-ac54-44e3-ac67-d86dd82e6bd9
langcode: en
status: true
dependencies:
  config:
    - field.field.node.modular_page.field_modules
    - field.field.node.modular_page.field_url
    - node.type.modular_page
  module:
    - entity_reference_revisions
    - user
id: node.modular_page.default
targetEntityType: node
bundle: modular_page
mode: default
content:
  field_modules:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 101
    region: content
  field_url:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 102
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
  search_api_excerpt: true
