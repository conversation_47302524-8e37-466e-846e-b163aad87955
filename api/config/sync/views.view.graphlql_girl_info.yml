uuid: 762cb558-26f0-474f-9201-891ba357f596
langcode: en
status: true
dependencies:
  config:
    - user.role.subscriber
  module:
    - datetime
    - graphql_views
    - pb_girl_info
    - statistics
    - user
id: graphlql_girl_info
label: 'GraphlQL / Girl Info'
module: views
description: ''
tag: ''
base_table: girl_info_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: name
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            subscriber: subscriber
            plus: plus
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        release:
          id: release
          table: girl_info_field_data
          field: release
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: release
          plugin_id: date
          order: DESC
          expose:
            label: Release
            field_identifier: release
          exposed: true
          granularity: second
        daycount:
          id: daycount
          table: girl_info_counter
          field: daycount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: 'Views today'
            field_identifier: daycount
          exposed: true
        totalcount:
          id: totalcount
          table: girl_info_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: 'Total views'
            field_identifier: totalcount
          exposed: true
      arguments: {  }
      filters:
        status:
          id: status
          table: girl_info_field_data
          field: status
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        descriptor_category:
          id: descriptor_category
          table: girl_info_field_data
          field: descriptor_category
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_category
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_category_op
            label: 'Descriptor category/title'
            description: ''
            use_operator: false
            operator: descriptor_category_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: descriptor_category
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        bustsize:
          id: bustsize
          table: girl_info_field_data
          field: bustsize
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: bustsize
          plugin_id: numeric
          operator: between
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: bustsize_op
            label: Bustsize
            description: ''
            use_operator: false
            operator: bustsize_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: bustsize
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        haircolor:
          id: haircolor
          table: girl_info_field_data
          field: haircolor
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: haircolor
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: haircolor_op
            label: 'Haircolor reference'
            description: ''
            use_operator: false
            operator: haircolor_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: haircolor
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: numeric
          operator: regular_expression
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: id_op
            label: ID
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        release:
          id: release
          table: girl_info_field_data
          field: release
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: release
          plugin_id: date
          operator: between
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: release_op
            label: Release
            description: ''
            use_operator: false
            operator: release_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: release
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_to_value_1:
          id: field_to_value_1
          table: girl_info__field_to
          field: field_to_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: empty
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 2
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_to_value:
          id: field_to_value
          table: girl_info__field_to
          field: field_to_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '<='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 2
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_from_value_1:
          id: field_from_value_1
          table: girl_info__field_from
          field: field_from_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: empty
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 3
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_from_value:
          id: field_from_value
          table: girl_info__field_from
          field: field_from_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 3
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
          3: OR
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags: {  }
  graphql_1:
    id: graphql_1
    display_title: GraphQL
    display_plugin: graphql
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      display_extenders: {  }
      graphql_query_name: pbGirlInfo
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags: {  }
