uuid: f0eafe06-a251-44ea-89cf-00c87f19bf0e
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_category
    - field.storage.girl_info.field_image_count
    - field.storage.girl_info.field_latest_gallery_release
    - field.storage.girl_info.field_main_focal_point_x
    - field.storage.girl_info.field_main_focal_point_y
    - field.storage.girl_info.field_plus_access
    - field.storage.girl_info.field_public_images
    - field.storage.girl_info.field_video_count
    - image.style.xlarge
    - taxonomy.vocabulary.category
    - user.role.administrator
  module:
    - pb_girl_info
    - rest
    - rest_views
    - serialization
    - statistics
    - svg_image
    - taxonomy
    - user
id: public_api_get_girl_infos
label: 'Public / API / GET / Girl Infos'
module: views
description: ''
tag: ''
base_table: girl_info_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: name
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        girl:
          id: girl
          table: girl_info_field_data
          field: girl
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: girl
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_entity_id
          settings: {  }
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        descriptor_month:
          id: descriptor_month
          table: girl_info_field_data
          field: descriptor_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_month
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        descriptor_year:
          id: descriptor_year
          table: girl_info_field_data
          field: descriptor_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_year
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_public_images:
          id: field_public_images
          table: girl_info__field_public_images
          field: field_public_images
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image_url
          settings:
            image_style: xlarge
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 1
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_image_count:
          id: field_image_count
          table: girl_info__field_image_count
          field: field_image_count
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_main_focal_point_x:
          id: field_main_focal_point_x
          table: girl_info__field_main_focal_point_x
          field: field_main_focal_point_x
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_main_focal_point_y:
          id: field_main_focal_point_y
          table: girl_info__field_main_focal_point_y
          field: field_main_focal_point_y
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_video_count:
          id: field_video_count
          table: girl_info__field_video_count
          field: field_video_count
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_category:
          id: field_category
          table: girl_info__field_category
          field: field_category
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_public_images_1:
          id: field_public_images_1
          table: girl_info__field_public_images
          field: field_public_images
          relationship: none
          group_type: group
          admin_label: width
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ field_public_images_1__width }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: width
          type: image_export
          settings:
            image_style: ''
            export_alt: 0
            export_title: false
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 1
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_public_images_2:
          id: field_public_images_2
          table: girl_info__field_public_images
          field: field_public_images
          relationship: none
          group_type: group
          admin_label: height
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ field_public_images_2__height }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_link: ''
            image_style: xlarge
            svg_attributes:
              width: null
              height: null
            svg_render_as_image: true
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 1
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_public_images_3:
          id: field_public_images_3
          table: girl_info__field_public_images
          field: field_public_images
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image_url
          settings:
            image_style: ''
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 1
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        firstname:
          id: firstname
          table: girl_info_field_data
          field: firstname
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: firstname
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_plus_access:
          id: field_plus_access
          table: girl_info__field_plus_access
          field: field_plus_access
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: custom
            format_custom_false: 'false'
            format_custom_true: 'true'
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_latest_gallery_release:
          id: field_latest_gallery_release
          table: girl_info__field_latest_gallery_release
          field: field_latest_gallery_release
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: 'Y-m-d\TH:i:sO'
            timezone: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        release:
          id: release
          table: girl_info_field_data
          field: release
          relationship: none
          group_type: count
          admin_label: ''
          entity_type: girl_info
          entity_field: release
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: girl_info_field_data
          field: status
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        field_category_target_id:
          id: field_category_target_id
          table: girl_info__field_category
          field: field_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: 'Category (field_category)'
            description: ''
            use_operator: false
            operator: field_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: category
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        description:
          id: description
          table: girl_info_field_data
          field: description
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: description
          plugin_id: string
          operator: contains
          value: ''
          group: 2
          exposed: true
          expose:
            operator_id: description_op
            label: Description
            description: ''
            use_operator: false
            operator: description_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: description
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: id_op
            label: ID
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 2
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_year:
          id: descriptor_year
          table: girl_info_field_data
          field: descriptor_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_year
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: descriptor_year_op
            label: 'Descriptor year'
            description: ''
            use_operator: false
            operator: descriptor_year_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: year
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_country_ref:
          id: descriptor_country_ref
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_country_ref_op
            label: 'Descriptor country reference'
            description: ''
            use_operator: false
            operator: descriptor_country_ref_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: descriptor_country_ref
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
              plus: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      style:
        type: serializer
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - url.query_args
        - user
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_image_count'
        - 'config:field.storage.girl_info.field_latest_gallery_release'
        - 'config:field.storage.girl_info.field_main_focal_point_x'
        - 'config:field.storage.girl_info.field_main_focal_point_y'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_public_images'
        - 'config:field.storage.girl_info.field_video_count'
  rest_export_1:
    id: rest_export_1
    display_title: 'REST export'
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 24
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      style:
        type: serializer
        options:
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            name:
              alias: ''
              raw_output: false
            id:
              alias: girl_info_id
              raw_output: false
            girl:
              alias: girl_id
              raw_output: false
            descriptor_month:
              alias: ''
              raw_output: false
            descriptor_year:
              alias: ''
              raw_output: false
            field_public_images:
              alias: field_public_images_low
              raw_output: false
            field_image_count:
              alias: ''
              raw_output: false
            field_main_focal_point_x:
              alias: ''
              raw_output: false
            field_main_focal_point_y:
              alias: ''
              raw_output: false
            field_video_count:
              alias: ''
              raw_output: false
            field_category:
              alias: ''
              raw_output: false
            field_public_images_1:
              alias: width
              raw_output: false
            field_public_images_2:
              alias: height
              raw_output: false
            field_public_images_3:
              alias: field_public_images
              raw_output: false
            firstname:
              alias: custom_access
              raw_output: false
      display_extenders: {  }
      path: api/v1/public/girl/infos
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - url.query_args
        - user
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_image_count'
        - 'config:field.storage.girl_info.field_latest_gallery_release'
        - 'config:field.storage.girl_info.field_main_focal_point_x'
        - 'config:field.storage.girl_info.field_main_focal_point_y'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_public_images'
        - 'config:field.storage.girl_info.field_video_count'
  rest_export_2:
    id: rest_export_2
    display_title: 'REST export'
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 24
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      sorts:
        totalcount:
          id: totalcount
          table: girl_info_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      style:
        type: serializer
        options:
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            name:
              alias: ''
              raw_output: false
            id:
              alias: girl_info_id
              raw_output: false
            girl:
              alias: girl_id
              raw_output: false
            descriptor_month:
              alias: ''
              raw_output: false
            descriptor_year:
              alias: ''
              raw_output: false
            field_public_images:
              alias: field_public_images_low
              raw_output: false
            field_image_count:
              alias: ''
              raw_output: false
            field_main_focal_point_x:
              alias: ''
              raw_output: false
            field_main_focal_point_y:
              alias: ''
              raw_output: false
            field_video_count:
              alias: ''
              raw_output: false
            field_category:
              alias: ''
              raw_output: false
            field_public_images_1:
              alias: width
              raw_output: false
            field_public_images_2:
              alias: height
              raw_output: false
            field_public_images_3:
              alias: field_public_images
              raw_output: false
            firstname:
              alias: custom_access
              raw_output: false
      defaults:
        sorts: false
      display_extenders: {  }
      path: api/v1/public/girl/popular-infos
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - url.query_args
        - user
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_image_count'
        - 'config:field.storage.girl_info.field_latest_gallery_release'
        - 'config:field.storage.girl_info.field_main_focal_point_x'
        - 'config:field.storage.girl_info.field_main_focal_point_y'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_public_images'
        - 'config:field.storage.girl_info.field_video_count'
  rest_export_3:
    id: rest_export_3
    display_title: 'REST export'
    display_plugin: rest_export
    position: 1
    display_options:
      fields:
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: count
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: null
          group_columns: null
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ''
          field_api_classes: false
          set_precision: false
          precision: 0
          decimal: .
          format_plural: 0
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 1
      style:
        type: serializer
        options:
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            id:
              alias: count
              raw_output: false
      defaults:
        group_by: false
        fields: false
      group_by: true
      display_extenders: {  }
      path: api/v1/public/girl/infos/count
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - request_format
        - url
        - user
      tags: {  }
  rest_export_4:
    id: rest_export_4
    display_title: 'REST export'
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 24
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      filters:
        status:
          id: status
          table: girl_info_field_data
          field: status
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        field_category_target_id:
          id: field_category_target_id
          table: girl_info__field_category
          field: field_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: 'Category (field_category)'
            description: ''
            use_operator: false
            operator: field_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: category
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        description:
          id: description
          table: girl_info_field_data
          field: description
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: description
          plugin_id: string
          operator: contains
          value: ''
          group: 2
          exposed: true
          expose:
            operator_id: description_op
            label: Description
            description: ''
            use_operator: false
            operator: description_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: description
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: id_op
            label: ID
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
              plus: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 2
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_year:
          id: descriptor_year
          table: girl_info_field_data
          field: descriptor_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_year
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: descriptor_year_op
            label: 'Descriptor year'
            description: ''
            use_operator: false
            operator: descriptor_year_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: year
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_country_ref:
          id: descriptor_country_ref
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_country_ref_op
            label: 'Descriptor country reference'
            description: ''
            use_operator: false
            operator: descriptor_country_ref_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: descriptor_country_ref
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
              plus: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      style:
        type: serializer
        options:
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            name:
              alias: ''
              raw_output: false
            id:
              alias: girl_info_id
              raw_output: false
            girl:
              alias: girl_id
              raw_output: false
            descriptor_month:
              alias: ''
              raw_output: false
            descriptor_year:
              alias: ''
              raw_output: false
            field_public_images:
              alias: field_public_images_low
              raw_output: false
            field_image_count:
              alias: ''
              raw_output: false
            field_main_focal_point_x:
              alias: ''
              raw_output: false
            field_main_focal_point_y:
              alias: ''
              raw_output: false
            field_video_count:
              alias: ''
              raw_output: false
            field_category:
              alias: ''
              raw_output: false
            field_public_images_1:
              alias: width
              raw_output: false
            field_public_images_2:
              alias: height
              raw_output: false
            field_public_images_3:
              alias: field_public_images
              raw_output: false
            firstname:
              alias: custom_access
              raw_output: false
      defaults:
        filters: false
        filter_groups: false
      display_extenders: {  }
      path: api/v1/public/girl/infos-by-date
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - url.query_args
        - user
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_image_count'
        - 'config:field.storage.girl_info.field_latest_gallery_release'
        - 'config:field.storage.girl_info.field_main_focal_point_x'
        - 'config:field.storage.girl_info.field_main_focal_point_y'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_public_images'
        - 'config:field.storage.girl_info.field_video_count'
  rest_export_5:
    id: rest_export_5
    display_title: 'REST export'
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 24
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      access:
        type: role
        options:
          role:
            administrator: administrator
      filters:
        field_category_target_id:
          id: field_category_target_id
          table: girl_info__field_category
          field: field_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: 'Category (field_category)'
            description: ''
            use_operator: false
            operator: field_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: category
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        description:
          id: description
          table: girl_info_field_data
          field: description
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: description
          plugin_id: string
          operator: contains
          value: ''
          group: 2
          exposed: true
          expose:
            operator_id: description_op
            label: Description
            description: ''
            use_operator: false
            operator: description_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: description
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: id_op
            label: ID
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 2
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_year:
          id: descriptor_year
          table: girl_info_field_data
          field: descriptor_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_year
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 2
          exposed: true
          expose:
            operator_id: descriptor_year_op
            label: 'Descriptor year'
            description: ''
            use_operator: false
            operator: descriptor_year_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: year
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_country_ref:
          id: descriptor_country_ref
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_country_ref_op
            label: 'Descriptor country reference'
            description: ''
            use_operator: false
            operator: descriptor_country_ref_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: descriptor_country_ref
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
              plus: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      style:
        type: serializer
        options:
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            name:
              alias: ''
              raw_output: false
            id:
              alias: girl_info_id
              raw_output: false
            girl:
              alias: girl_id
              raw_output: false
            descriptor_month:
              alias: ''
              raw_output: false
            descriptor_year:
              alias: ''
              raw_output: false
            field_public_images:
              alias: field_public_images_low
              raw_output: false
            field_image_count:
              alias: ''
              raw_output: false
            field_main_focal_point_x:
              alias: ''
              raw_output: false
            field_main_focal_point_y:
              alias: ''
              raw_output: false
            field_video_count:
              alias: ''
              raw_output: false
            field_category:
              alias: ''
              raw_output: false
            field_public_images_1:
              alias: width
              raw_output: false
            field_public_images_2:
              alias: height
              raw_output: false
            field_public_images_3:
              alias: field_public_images
              raw_output: false
            firstname:
              alias: custom_access
              raw_output: false
      defaults:
        access: false
        filters: false
        filter_groups: false
      display_extenders: {  }
      path: api/v1/admin/girl/infos
      auth: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - url.query_args
        - user
        - user.roles
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_image_count'
        - 'config:field.storage.girl_info.field_latest_gallery_release'
        - 'config:field.storage.girl_info.field_main_focal_point_x'
        - 'config:field.storage.girl_info.field_main_focal_point_y'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_public_images'
        - 'config:field.storage.girl_info.field_video_count'
