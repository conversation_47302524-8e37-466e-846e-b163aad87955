uuid: ab004b97-e7e4-43de-b429-daff6ae0de18
langcode: en
status: true
dependencies:
  config:
    - media.type.gallery
    - user.role.administrator
    - user.role.subscriber
  module:
    - media
    - rest
    - serialization
    - statistics
    - user
id: most_popular_gallery
label: 'API / GET / Most Popular (Gallery)'
module: views
description: ''
tag: ''
base_table: media_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        mid:
          id: mid
          table: media_field_data
          field: mid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: mid
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        daycount:
          id: daycount
          table: media_counter
          field: daycount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: statistics_numeric
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            subscriber: subscriber
            plus: plus
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        daycount:
          id: daycount
          table: media_counter
          field: daycount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: daycount
          exposed: false
      arguments: {  }
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          entity_type: media
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          entity_type: media
          entity_field: bundle
          plugin_id: bundle
          value:
            gallery: gallery
          expose:
            operator_limit_selection: false
            operator_list: {  }
      style:
        type: serializer
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url.query_args
        - user.roles
      tags: {  }
  rest_export_1:
    id: rest_export_1
    display_title: 'Gallery (Recent)'
    display_plugin: rest_export
    position: 1
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 10
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            name:
              alias: ''
              raw_output: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/popular/total/gallery
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user.roles
      tags: {  }
  rest_export_2:
    id: rest_export_2
    display_title: 'Gallery (Total)'
    display_plugin: rest_export
    position: 1
    display_options:
      fields:
        mid:
          id: mid
          table: media_field_data
          field: mid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: mid
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        totalcount:
          id: totalcount
          table: media_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: statistics_numeric
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 10
      sorts:
        totalcount:
          id: totalcount
          table: media_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: totalcount
          exposed: false
      style:
        type: serializer
        options:
          uses_fields: false
          formats:
            json: json
      row:
        type: data_field
        options:
          field_options:
            name:
              alias: ''
              raw_output: false
      defaults:
        fields: false
        sorts: false
      display_description: ''
      display_extenders: {  }
      path: api/v1/popular/recent/gallery
      auth:
        - cookie
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user.roles
      tags: {  }
