uuid: bb0ea58e-1e53-4615-a4d4-3b94f464edaa
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_tags
    - taxonomy.vocabulary.tags
  module:
    - pb_girl_info
id: girl_info.girl_info.field_tags
field_name: field_tags
entity_type: girl_info
bundle: girl_info
label: Tags
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      tags: tags
    sort:
      field: name
      direction: asc
    auto_create: true
    auto_create_bundle: ''
field_type: entity_reference
