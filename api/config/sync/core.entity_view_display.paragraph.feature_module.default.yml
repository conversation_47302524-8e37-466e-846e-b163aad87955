uuid: b963ad71-4df7-41c5-ad58-567eb253f835
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.feature_module.field_button
    - field.field.paragraph.feature_module.field_button_bool
    - field.field.paragraph.feature_module.field_category_bool
    - field.field.paragraph.feature_module.field_category_reference
    - field.field.paragraph.feature_module.field_descriptor_country
    - field.field.paragraph.feature_module.field_girl_info
    - field.field.paragraph.feature_module.field_grid_layout
    - field.field.paragraph.feature_module.field_sorting
    - field.field.paragraph.feature_module.field_title
    - paragraphs.paragraphs_type.feature_module
  module:
    - link
    - options
id: paragraph.feature_module.default
targetEntityType: paragraph
bundle: feature_module
mode: default
content:
  field_button:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 7
    region: content
  field_button_bool:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 6
    region: content
  field_category_bool:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 1
    region: content
  field_category_reference:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 2
    region: content
  field_descriptor_country:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 8
    region: content
  field_girl_info:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 5
    region: content
  field_grid_layout:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
  field_sorting:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  field_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  search_api_excerpt: true
