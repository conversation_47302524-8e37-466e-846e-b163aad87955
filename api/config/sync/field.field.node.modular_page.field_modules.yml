uuid: fb31cc74-fad3-48bb-88aa-75d32d6de92c
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_modules
    - node.type.modular_page
    - paragraphs.paragraphs_type.card_grid
    - paragraphs.paragraphs_type.coupon_card
    - paragraphs.paragraphs_type.homepage_features
    - paragraphs.paragraphs_type.nexx_video
  module:
    - entity_reference_revisions
id: node.modular_page.field_modules
field_name: field_modules
entity_type: node
bundle: modular_page
label: Modules
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      card_grid: card_grid
      coupon_card: coupon_card
      homepage_features: homepage_features
      nexx_video: nexx_video
    negate: 1
    target_bundles_drag_drop:
      card_grid:
        weight: 6
        enabled: true
      coupon_card:
        weight: 7
        enabled: true
      feature_module:
        weight: 8
        enabled: false
      homepage_features:
        weight: 9
        enabled: true
      nexx_video:
        weight: 10
        enabled: true
field_type: entity_reference_revisions
