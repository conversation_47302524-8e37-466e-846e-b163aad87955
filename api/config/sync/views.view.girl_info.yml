uuid: 0658c963-1ce0-4d0e-b63d-01ed94a16264
langcode: en
status: true
dependencies:
  config:
    - field.storage.girl_info.field_category
    - field.storage.girl_info.field_export_girl_des_tages
    - field.storage.girl_info.field_from
    - field.storage.girl_info.field_plus_access
    - field.storage.girl_info.field_tags
    - field.storage.media.field_media_image
    - image.style.thumbnail
    - taxonomy.vocabulary.category
    - taxonomy.vocabulary.city
    - taxonomy.vocabulary.country
    - taxonomy.vocabulary.province
    - taxonomy.vocabulary.tags
    - user.role.administrator
  module:
    - better_exposed_filters
    - datetime
    - media
    - pb_girl_info
    - statistics
    - svg_image
    - taxonomy
    - user
id: girl_info
label: 'Admin Page / Girl Info'
module: views
description: ''
tag: ''
base_table: girl_info_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Girl Info'
      fields:
        girl_info_bulk_form:
          id: girl_info_bulk_form
          table: girl_info
          field: girl_info_bulk_form
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          plugin_id: bulk_form
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          action_title: Action
          include_exclude: exclude
          selected_actions: {  }
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: field
          label: ID
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_media_image:
          id: field_media_image
          table: media__field_media_image
          field: field_media_image
          relationship: main_images_target_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Image
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_link: ''
            image_style: thumbnail
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: name
          plugin_id: field
          label: Name
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        girl:
          id: girl
          table: girl_info_field_data
          field: girl
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: girl
          plugin_id: field
          label: Girl
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_category:
          id: field_category
          table: girl_info__field_category
          field: field_category
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Category
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        totalcount:
          id: totalcount
          table: girl_info_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: statistics_numeric
          label: 'Total views'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ''
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        daycount:
          id: daycount
          table: girl_info_counter
          field: daycount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: statistics_numeric
          label: 'Views today'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        field_tags:
          id: field_tags
          table: girl_info__field_tags
          field: field_tags
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Tags
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        descriptor_country_ref:
          id: descriptor_country_ref
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: field
          label: 'Ausgaben Land'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_plus_access:
          id: field_plus_access
          table: girl_info__field_plus_access
          field: field_plus_access
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Plus Access'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_export_girl_des_tages:
          id: field_export_girl_des_tages
          table: girl_info__field_export_girl_des_tages
          field: field_export_girl_des_tages
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Export to Playboy'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        status:
          id: status
          table: girl_info_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: status
          plugin_id: field
          label: Status
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: custom
            format_custom_false: Unpublished
            format_custom_true: Published
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_from:
          id: field_from
          table: girl_info__field_from
          field: field_from
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Publishing Date'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_default
          settings:
            timezone_override: ''
            format_type: medium
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        release:
          id: release
          table: girl_info_field_data
          field: release
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: release
          plugin_id: field
          label: 'Release Date'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        changed:
          id: changed
          table: girl_info_field_data
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: changed
          plugin_id: field
          label: Changed
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        operations:
          id: operations
          table: girl_info
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          plugin_id: entity_operations
          label: 'Operations links'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 50
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
            filter:
              name:
                plugin_id: default
                advanced:
                  placeholder_text: ''
                  collapsible: false
                  is_secondary: false
              status:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
              field_featured_value:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
              field_city_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
              field_tags_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
              field_province_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
              field_country_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
              field_category_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                  collapsible: false
                  is_secondary: false
      access:
        type: role
        options:
          role:
            administrator: administrator
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: standard
          order: DESC
          expose:
            label: ID
            field_identifier: id
          exposed: true
        totalcount:
          id: totalcount
          table: girl_info_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: 'Total views'
            field_identifier: totalcount
          exposed: true
        publish_date:
          id: publish_date
          table: girl_info_field_data
          field: publish_date
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: publish_date
          plugin_id: standard
          order: ASC
          expose:
            label: 'Publish date'
            field_identifier: publish_date
          exposed: true
      arguments: {  }
      filters:
        name:
          id: name
          table: girl_info_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: true
            multiple: false
            remember_roles:
              administrator: administrator
              anonymous: '0'
              authenticated: '0'
              subscriber: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        id:
          id: id
          table: girl_info_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: id
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: id_op
            label: ID
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: girl_info_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Published
            description: null
            use_operator: false
            operator: status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: status
            required: true
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: true
          group_info:
            label: Status
            description: ''
            identifier: status
            optional: true
            widget: select
            multiple: false
            remember: true
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: Published
                operator: '='
                value: '1'
              2:
                title: Unpublished
                operator: '='
                value: '0'
        field_featured_value:
          id: field_featured_value
          table: girl_info__field_featured
          field: field_featured_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Featured (field_featured)'
            description: null
            use_operator: false
            operator: field_featured_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_featured_value
            required: true
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: true
          group_info:
            label: Featured
            description: ''
            identifier: field_featured_value
            optional: true
            widget: select
            multiple: false
            remember: true
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: 'Yes'
                operator: '='
                value: '1'
              2:
                title: 'No'
                operator: '='
                value: '0'
        field_city_target_id:
          id: field_city_target_id
          table: girl_info__field_city
          field: field_city_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_city_target_id_op
            label: 'City (field_city)'
            description: ''
            use_operator: false
            operator: field_city_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_city_target_id
            required: false
            remember: true
            multiple: false
            remember_roles:
              administrator: administrator
              anonymous: '0'
              authenticated: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: city
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_tags_target_id:
          id: field_tags_target_id
          table: girl_info__field_tags
          field: field_tags_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_tags_target_id_op
            label: 'Tags (field_tags)'
            description: ''
            use_operator: false
            operator: field_tags_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_tags_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: tags
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_province_target_id:
          id: field_province_target_id
          table: girl_info__field_province
          field: field_province_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_province_target_id_op
            label: 'Province (field_province)'
            description: ''
            use_operator: false
            operator: field_province_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_province_target_id
            required: false
            remember: true
            multiple: false
            remember_roles:
              administrator: administrator
              anonymous: '0'
              authenticated: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: province
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_country_target_id:
          id: field_country_target_id
          table: girl_info__field_country
          field: field_country_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_country_target_id_op
            label: 'Country (field_country)'
            description: ''
            use_operator: false
            operator: field_country_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_country_target_id
            required: false
            remember: true
            multiple: false
            remember_roles:
              administrator: administrator
              anonymous: '0'
              authenticated: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: country
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_category_target_id:
          id: field_category_target_id
          table: girl_info__field_category
          field: field_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: Category
            description: ''
            use_operator: false
            operator: field_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_category_target_id
            required: false
            remember: true
            multiple: true
            remember_roles:
              administrator: administrator
              anonymous: '0'
              authenticated: '0'
              subscriber: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: category
          type: select
          hierarchy: true
          limit: true
          error_message: true
        haircolor:
          id: haircolor
          table: girl_info_field_data
          field: haircolor
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: haircolor
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: haircolor_op
            label: 'Haircolor reference'
            description: ''
            use_operator: false
            operator: haircolor_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: haircolor
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        eyecolor:
          id: eyecolor
          table: girl_info_field_data
          field: eyecolor
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: eyecolor
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: eyecolor_op
            label: 'Eyecolor reference'
            description: ''
            use_operator: false
            operator: eyecolor_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: eyecolor
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_month:
          id: descriptor_month
          table: girl_info_field_data
          field: descriptor_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_month
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_month_op
            label: 'Ausgaben Monat'
            description: ''
            use_operator: true
            operator: descriptor_month_op
            operator_limit_selection: true
            operator_list:
              '<=': '<='
              '=': '='
              '>=': '>='
            identifier: descriptor_month
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_year:
          id: descriptor_year
          table: girl_info_field_data
          field: descriptor_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_year
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_year_op
            label: 'Ausgaben Jahr'
            description: ''
            use_operator: true
            operator: descriptor_year_op
            operator_limit_selection: true
            operator_list:
              '<=': '<='
              '=': '='
              '>=': '>='
            identifier: descriptor_year
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        descriptor_country_ref:
          id: descriptor_country_ref
          table: girl_info_field_data
          field: descriptor_country_ref
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: girl_info
          entity_field: descriptor_country_ref
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: descriptor_country_ref_op
            label: 'Ausgaben Land'
            description: 'Hier muss die ID angegeben werden (DE = 10835)'
            use_operator: true
            operator: descriptor_country_ref_op
            operator_limit_selection: true
            operator_list:
              '=': '='
              '!=': '!='
            identifier: descriptor_country_ref
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_export_girl_des_tages_value:
          id: field_export_girl_des_tages_value
          table: girl_info__field_export_girl_des_tages
          field: field_export_girl_des_tages_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Export to Playboy (GdT-Feed)'
            description: ''
            use_operator: false
            operator: field_export_girl_des_tages_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_export_girl_des_tages_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_plus_access_value:
          id: field_plus_access_value
          table: girl_info__field_plus_access
          field: field_plus_access_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Plus Access'
            description: ''
            use_operator: false
            operator: field_plus_access_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_plus_access_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              subscriber: '0'
              mexo: '0'
              plus: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            id: id
            field_media_image: field_media_image
            name: name
            girl: girl
            field_tags: field_tags
            status: status
            field_from: field_from
            release: release
            changed: changed
            operations: operations
          default: id
          info:
            id:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_media_image:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            name:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            girl:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_tags:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            status:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_from:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            release:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            changed:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        main_images_target_id:
          id: main_images_target_id
          table: girl_info__main_images
          field: main_images_target_id
          relationship: none
          group_type: group
          admin_label: Media
          entity_type: girl_info
          entity_field: main_images
          plugin_id: standard
          required: false
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: "<div id=\"block-gin-local-actions\">\r\n  \r\n    \r\n        <ul class=\"local-actions\">\r\n      <li class=\"local-actions__item\"><a href=\"/admin/structure/girl_info/add\" class=\"button button--action button--primary\" data-drupal-link-system-path=\"admin/structure/girl_info/add\">Add Girl info</a></li>\r\n\r\n    </ul>\r\n  </div>"
            format: full_html
          tokenize: false
        result:
          id: result
          table: views
          field: result
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: result
          empty: false
          content: 'Displaying @start - @end of @total'
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user
        - user.roles
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_export_girl_des_tages'
        - 'config:field.storage.girl_info.field_from'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_tags'
        - 'config:field.storage.media.field_media_image'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: admin/content/girl-infos
      menu:
        type: tab
        title: 'Girl Infos'
        description: ''
        weight: -20
        expanded: false
        menu_name: admin
        parent: system.admin_content
        context: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user
        - user.roles
      tags:
        - 'config:field.storage.girl_info.field_category'
        - 'config:field.storage.girl_info.field_export_girl_des_tages'
        - 'config:field.storage.girl_info.field_from'
        - 'config:field.storage.girl_info.field_plus_access'
        - 'config:field.storage.girl_info.field_tags'
        - 'config:field.storage.media.field_media_image'
