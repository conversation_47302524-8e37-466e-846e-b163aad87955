uuid: 7ced296f-bc36-4f95-a136-0e5d4113db80
langcode: en
status: true
dependencies:
  config:
    - field.field.girl.girl.field_from
    - field.field.girl.girl.field_notes
    - field.field.girl.girl.field_release_date
    - field.field.girl.girl.field_to
  module:
    - datetime
    - media_library
    - media_library_edit
    - pb_girl
    - text
id: girl.girl.default
targetEntityType: girl
bundle: girl
mode: default
content:
  field_from:
    type: datetime_default
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_notes:
    type: text_textarea
    weight: 10
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_release_date:
    type: datetime_default
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_to:
    type: datetime_default
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  firstname:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  girl_infos:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  images:
    type: image_image
    weight: -4
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  lastname:
    type: string_textfield
    weight: 4
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  main_images:
    type: media_library_widget
    weight: 5
    region: content
    settings:
      media_types: {  }
    third_party_settings:
      media_library_edit:
        show_edit: '1'
  middlename:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  non_nude_images:
    type: media_library_widget
    weight: 6
    region: content
    settings:
      media_types: {  }
    third_party_settings:
      media_library_edit:
        show_edit: '1'
hidden: {  }
