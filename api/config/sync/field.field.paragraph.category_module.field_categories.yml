uuid: 8d780d61-6b04-4821-b333-151b4c4c399b
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_categories
    - paragraphs.paragraphs_type.category_module
    - taxonomy.vocabulary.category
id: paragraph.category_module.field_categories
field_name: field_categories
entity_type: paragraph
bundle: category_module
label: Categories
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      category: category
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
