import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';

@Injectable({
  providedIn: 'root'
})
export class UtmService {
  private readonly cookieService = inject(CookieService);
  private readonly platformId = inject(PLATFORM_ID);

  private readonly utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];

  /**
   * Handles UTM parameters and sets cookies for tracking
   * @param upScoreObjectId - The object_id from upScore data (e.g., 'aa_6')
   */
  handleUTM(upScoreObjectId?: string): void {
    if (!isPlatformBrowser(this.platformId)) {
      return; // Only run in browser
    }

    const urlParams = new URLSearchParams(window.location.search);
    const isLocalhost = window.location.hostname === 'localhost';

    // Get the current URL without query parameters
    const currentUrlWithoutParams = window.location.origin + window.location.pathname;

    // Determine cookie options
    const cookieOptions = {
      path: '/',
      domain: isLocalhost ? undefined : '.playboy.de'
    };

    // Set the source_id cookie
    this.cookieService.set('source_id', currentUrlWithoutParams, cookieOptions);

    // Set UTM cookies
    this.utmParams.forEach(param => {
      const value = urlParams.get(param);
      if (value) {
        this.cookieService.set(param, value, cookieOptions);
      }
    });

    // Set the upscore_object_id cookie if provided
    if (upScoreObjectId) {
      this.cookieService.set('upscore_object_id', upScoreObjectId, cookieOptions);
    }
  }
}
