import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IPreview } from '../models/preview';
import { parseNumberOrNull } from '../utils/parseNumberOrNull';
import { ApiService } from './api.service';

export interface ApiPublicGirlAdditionalGallery {
  id: string;
  name: string;
  description: string;
  eyecolor: string;
  haircolor: string;
  name_1: string;
  main_images_target_id: string;
  field_category: string;
  descriptor_month: string;
  descriptor_year: string;
  field_public_images: string;
  release: string;
  birthday: string;
  bustsize: string;
  field_city: string;
  field_country: string;
  eyecolor_1: string;
  haircolor_1: string;
  height: string;
  hipsize: string;
  field_province: string;
  waistsize: string;
  weight: string;
  field_credit: string;
  firstname: string;
  lastname: string;
  field_image_count: string;
  field_main_focal_point_x: number;
  field_main_focal_point_y: number;
  field_video_count: string;
  field_category_1: string;
  field_country_1: string;
  descriptor_country_ref: string;
  girl_id: string;
}

export interface PublicGirlAdditionalGallery {
  id: string;
  girl_id: string;
  image: string;
  fieldImageCount: number | null;
  fieldVideoCount: number | null;
  focalPoint: { x: number; y: number };
  release: string;
  field_category: string;
  field_credit: string;
}

export interface PublicGirl {
  descriptor_country_ref: string;
  id: string;
  lastname: string;
  firstname: string;
  field_category: string;
  field_credit: string;
  field_tags: string[];
  field_release_date: string;
  release: string;
  height: string;
  birthday: string;
  bustsize: string;
  field_city: string;
  field_country: string;
  eyecolor_1: string;
  haircolor_1: string;
  weight: string;
  field_province: string;
  waistsize: string;
  hipsize: string;
  name: string;
  description: string;
  images: any[];
  field_main_focal_point_x: number;
  field_main_focal_point_y: number;
  field_image_count: number;
  field_video_count: number;
}

export interface PublicGirlInfo {
  descriptor_country_ref: string;
  focal: { x: number; y: number };
  field_category: string;
  descriptor_month: number;
  descriptor_year: number;
  field_credit: string;
  field_tags: string[];
  release: string;
  name: string;
  name_1: string;
  description: string;
  images: string[];
  girl_id: string;
  field_main_focal_point_x: string;
  field_main_focal_point_y: string;
  field_third_focal_point_x: string;
  field_third_focal_point_y: string;
  field_second_focal_point_x: string;
  field_second_focal_point_y: string;
  field_image_count: number;
  field_video_count: number;
  field_plus_access?: boolean;
  field_latest_gallery_release?: string;
}

@Injectable({ providedIn: 'root' })
export class PublicService {
  private readonly behav = new BehaviorSubject<PublicGirl | undefined>(
    undefined,
  );

  constructor(private api: ApiService) {}

  public getGirl(id: string): Observable<PublicGirl> {
    return this.api
      .get<
        Array<{
          descriptor_country_ref: string;
          name: string;
          description: string;
          field_public_images: string;
          weight: string;
          waistsize: string;
          field_province: string;
          hipsize: string;
          height: string;
          haircolor_1: string;
          eyecolor_1: string;
          field_country: string;
          field_city: string;
          bustsize: string;
          birthday: string;
          release: string;
          field_tags: string[];
          field_credit: string;
          field_category: string;
          firstname: string;
          lastname: string;
          field_main_focal_point_x: number;
          field_main_focal_point_y: number;
          field_image_count: number;
          field_video_count: number;
          id: string;
        }>
      >('public/girl/' + id)
      .pipe(
        map((dataArray) => {
          const reversedDataArray = [...dataArray].reverse();
          if (reversedDataArray && reversedDataArray.length > 0) {
            const images = reversedDataArray.map((item) => ({
              girlInfoId: item.id,
              image: item.field_public_images.trim(),
              fieldImageCount: parseNumberOrNull(item?.field_image_count),
              fieldVideoCount: parseNumberOrNull(item?.field_video_count),
              focalPoint: {
                x: item.field_main_focal_point_x,
                y: item.field_main_focal_point_y,
              },
              release: item.release,
              field_category: item.field_category,
              field_credit: item.field_credit,
            }));

            // Basisdaten vom ersten Eintrag
            const baseData = reversedDataArray[0];

            // Summierte Felder
            const totalImageCount = reversedDataArray.reduce(
              (acc, item) => acc + Number(item.field_image_count || 0),
              0,
            );
            const totalVideoCount = reversedDataArray.reduce(
              (acc, item) => acc + Number(item.field_video_count || 0),
              0,
            );

            return {
              name: baseData.name,
              description: baseData.description,
              weight: baseData.weight,
              waistsize: baseData.waistsize,
              field_province: baseData.field_province,
              hipsize: baseData.hipsize,
              height: baseData.height,
              haircolor_1: baseData.haircolor_1,
              eyecolor_1: baseData.eyecolor_1,
              descriptor_country_ref: baseData.descriptor_country_ref,
              field_city: baseData.field_city,
              bustsize: baseData.bustsize,
              birthday: baseData.birthday,
              images: images, // Hier nun alle Bilder
              release: baseData.release,
              field_tags: baseData.field_category,
              field_credit: baseData.field_credit,
              field_category: baseData.field_category,
              firstname: baseData.firstname,
              lastname: baseData.lastname,
              focal: JSON.stringify({
                x: baseData.field_main_focal_point_x,
                y: baseData.field_main_focal_point_y,
              }),
              field_image_count: totalImageCount,
              field_video_count: totalVideoCount,
              id: baseData.id,
            } as unknown as PublicGirl;
          }
          return {} as PublicGirl;
        }),
      );
  }

  public getGirlAdditionalGalleries(id: string): Observable<PublicGirlAdditionalGallery[]> {
    return this.api.get<ApiPublicGirlAdditionalGallery[]>('public/girl-additional-galleries/' + id).pipe(
      map(res => {
        const galleries = res.map((item) => ({
          id: item.id,
          girl_id: item.girl_id,
          image: item.field_public_images.trim(),
          fieldImageCount: parseNumberOrNull(item?.field_image_count),
          fieldVideoCount: parseNumberOrNull(item?.field_video_count),
          focalPoint: {
                x: parseNumberOrNull(item.field_main_focal_point_x),
                y: parseNumberOrNull(item.field_main_focal_point_y),
              },
          release: item.release,
          field_category: item.field_category,
          field_credit: item.field_credit,
        }));

        return galleries;
      }),
    );
  }

  public getGirlInfo(id: string): Observable<PublicGirlInfo> {
    return this.api
      .get<
        [
          {
            name: string;
            name_1: string;
            description: string;
            field_public_images: string;
            release: string;
            field_plus_access?: string | boolean;
            field_credit: string;
            field_tags: string[];
            girl_id: string;
            field_category: string;
            descriptor_month: string;
            descriptor_year: string;
            descriptor_country_ref: string;
            field_main_focal_point_x: string;
            field_main_focal_point_y: string;
            field_second_focal_point_x: string;
            field_second_focal_point_y: string;
            field_third_focal_point_x: string;
            field_third_focal_point_y: string;
            field_image_count: number;
            field_video_count: number;
            id: string;
            field_latest_gallery_release?: string;
          },
        ]
      >('public/girl/info/' + id)
      .pipe(
        map(([data]) => {
          if (!!data && !!data.name) {
            return {
              name: data.name,
              name_1: data.name_1,
              description: data.description,
              release: data.release,
              field_plus_access:
                data.field_plus_access === 'true' ||
                data.field_plus_access === true,
              field_credit: data.field_credit,
              field_tags: data.field_tags,
              girl_id: data.girl_id,
              focal: JSON.stringify({
                x: data.field_main_focal_point_x,
                y: data.field_main_focal_point_y,
              }),
              descriptor_month: Number(data.descriptor_month),
              descriptor_year: Number(data.descriptor_year),
              field_main_focal_point_x: data.field_main_focal_point_x,
              field_main_focal_point_y: data.field_main_focal_point_y,
              field_second_focal_point_x: data.field_second_focal_point_x,
              field_second_focal_point_y: data.field_second_focal_point_y,
              field_third_focal_point_x: data.field_third_focal_point_x,
              field_third_focal_point_y: data.field_third_focal_point_y,
              field_image_count: data.field_image_count,
              field_video_count: data.field_video_count,
              field_category: data.field_category,
              descriptor_country_ref: data.descriptor_country_ref,
              images: data.field_public_images
                .split(',')
                .map((image: string) => image.trim()),
              id: data.id,
              field_latest_gallery_release: data.field_latest_gallery_release
            } as unknown as PublicGirlInfo;
          }
          return {} as PublicGirlInfo;
        }),
      );
  }

  public getHomeFeatured(): Observable<IPreview[]> {
    return this.api
      .get<
        [
          {
            name: string;
            id: string;
            field_public_images: string;
            field_main_focal_point_x: number;
            field_main_focal_point_y: number;
            descriptor_month: string;
            descriptor_year: string;
            link?: (string | number)[];
          },
        ]
      >('public/home/<USER>/')
      .pipe(
        map((data) => {
          return data.map((item) => ({
            id: item.id,
            title: 'Die ganze Welt der schönen Frauen',
            text: item.name
              .replaceAll('&quot;', '"')
              .replaceAll('&amp;', '&')
              .replaceAll('&#039;', "'"),
            image: item.field_public_images,
            link: ['/p/girl/info/' + item.id],
            focalPoint: {
              x: item.field_main_focal_point_x,
              y: item.field_main_focal_point_y,
            },
            month: item.descriptor_month,
            year: parseInt(item.descriptor_year),
          }));
        }),
      );
  }

  public getGirlInfos(
    page: number,
    category_id: string[] = null,
    searchTerm: String = null,
    order?: 'popular' | 'by-date',
    descriptor_country_ref?: string,
  ): Observable<IPreview[]> {
    // fetch specific page
    let query = '?page=' + page;
    if (!!category_id && category_id.length > 0) {
      query += '&category[]=' + category_id.join('&category[]=');
    }
    // pass search term
    if (!!searchTerm) {
      query += `&name=${searchTerm}&year=${searchTerm}&description=${searchTerm}&id=${searchTerm}`;
    }
    const url = order === 'by-date' ? 'public/girl/infos-by-date' : order === 'popular' ? 'public/girl/popular-infos' : 'public/girl/infos';
    if (order && descriptor_country_ref) {
      query += `&descriptor_country_ref=${descriptor_country_ref}`;
    }
    return this.api
      .get<
        [
          {
            name: string;
            girl_info_id: string;
            girl_id: string;
            descriptor_month: string;
            descriptor_year: string;
            field_public_images_low: string;
            field_image_count: string;
            field_main_focal_point_x: string;
            field_main_focal_point_y: string;
            field_video_count: number;
            field_category: string;
            width: string;
            height: string;
            field_public_images: string;
            field_latest_gallery_release?: string
          },
        ]
      >(url + query)
      .pipe(
        map((data) => {
          if (!!data && data.length > 0) {
            return data.map((item) => {
              return {
                id: item.girl_info_id,
                text: item.name
                  .replaceAll('&quot;', '"')
                  .replaceAll('&amp;', '&')
                  .replaceAll('&#039;', "'"),
                meta: {
                  images: parseNumberOrNull(item.field_image_count) || 0,
                  videos: parseNumberOrNull(item.field_video_count) || 0,
                  girlInfos: 0,
                },
                title: item.field_category,
                image: item.field_public_images,
                imageRatio: parseInt(item.width) / parseInt(item.height),
                imageLowRes: item.field_public_images_low,
                link: ['/p/girl/info', item.girl_info_id],
                month: item.descriptor_month,
                year: parseInt(item.descriptor_year),
                focalPoint: {
                  x: parseInt(item.field_main_focal_point_x),
                  y: parseInt(item.field_main_focal_point_y),
                },
                field_latest_gallery_release: item.field_latest_gallery_release
              } as IPreview;
            }) as IPreview[];
          }
          return [] as IPreview[];
        }),
      );
  }

  public getGirlInfosCount(category_id: string[] = null): Observable<number> {
    let query = '';
    if (!!category_id && category_id.length > 0) {
      query = '?category[]=' + category_id.join('&category[]=');
    }
    return this.api
      .get<
        [
          {
            count: string;
          },
        ]
      >('public/girl/infos/count' + query)
      .pipe(
        map((data) => {
          if (!!data && data.length > 0) {
            return parseInt(data[0].count) / 12;
          }
          return 0;
        }),
      );
  }

  public getGalleryOfDay(): Observable<IPreview[]> {
    return this.api
      .get<
        [
          {
            name: string;
            id: string;
            girl: string;
            descriptor_month: string;
            descriptor_year: string;
            field_main_focal_point_x: string;
            field_main_focal_point_y: string;
            field_category: string;
            field_public_images: string;
          },
        ]
      >('public/galleryOfDay')
      .pipe(
        map((data) => {
          if (!!data && data.length > 0) {
            return data.map((item) => {
              const image = item.field_public_images?.split(',')?.[0];
              return {
                id: item.id,
                text: item.name
                  .replaceAll('&quot;', '"')
                  .replaceAll('&amp;', '&')
                  .replaceAll('&#039;', "'"),
                title: item.field_category,
                image,
                link: ['/p/girl/info', item.id],
                month: item.descriptor_month,
                year: parseInt(item.descriptor_year),
                focalPoint: {
                  x: parseInt(item.field_main_focal_point_x),
                  y: parseInt(item.field_main_focal_point_y),
                },
              } as IPreview;
            }) as IPreview[];
          }
          return [] as IPreview[];
        }),
      );
  }
}
