import { Injectable } from '@angular/core';
import { distinctJSON } from '@pb/ui';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged, filter, map } from 'rxjs/operators';
import { ICardCarouselSlideData } from '../shared/components/card-carousel/card-carousel-slide.directive';
import { GetServerImageUrl } from '../utils/serverImage';
import { ApiService } from './api.service';

export interface IPromoData {
  data: any;
  headerGalleries: string[];
  ads: ICardCarouselSlideData[];
  previewVideo: {
    nexxID: string;
    title: string;
  };
  banner: {
    image: string;
    title: string;
    price: string;
  };
  gridFeatures: Array<{
    icon: string;
    name: string;
  }>;
  isMaintainance: boolean;
}

@Injectable({ providedIn: 'root' })
export class PromoService {
  private readonly behav = new BehaviorSubject<IPromoData | undefined>(
    undefined,
  );

  get Data(): Observable<IPromoData> {
    return this.behav.pipe(
      filter((v) => !!v),
      map((v) => v!),
      distinctJSON(),
    );
  }

  $isMaintainance = this.Data.pipe(
    map((v) => v.isMaintainance),
    distinctUntilChanged((a, b) => a === b),
  );

  constructor(api: ApiService) {
    api
      .get<
        [
          {
            field_banner_price: string;
            field_banner_title: string;
            field_grid_subtitle1: string;
            field_grid_subtitle2: string;
            field_grid_subtitle3: string;
            field_grid_title1: string;
            field_grid_title2: string;
            field_grid_title3: string;
            field_media_image_1: string;
            field_nexx_id: string;
            field_slideshow_export: string[];
            field_subtitle: string;
            field_maintenance_mode: '1' | '0';
            field_title: string;
            field_video_title: string;
            field_features_export: Array<{ icon: string; name: string }>;
          },
        ]
      >('homepage')
      .pipe(
        map(
          ([data]) =>
            ({
              data,
              headerGalleries:
                data.field_slideshow_export.map(GetServerImageUrl),
              ads: [
                {
                  text: data.field_grid_subtitle1,
                  title: data.field_grid_title1,
                  image: 'assets/examples/heisse-bilder.png',
                },
                {
                  text: data.field_grid_subtitle2,
                  title: data.field_grid_title2,
                  image: 'assets/examples/exklusive-inhalte.jpeg',
                },
                {
                  text: data.field_grid_subtitle3,
                  title: data.field_grid_title3,
                  image: 'assets/examples/taeglich-neues.jpeg',
                  new: true,
                },
              ],
              previewVideo: {
                nexxID: data.field_nexx_id,
                title: data.field_video_title,
              },
              banner: {
                price: data.field_banner_price,
                title: data.field_banner_title,
                image: GetServerImageUrl(data.field_media_image_1),
              },
              gridFeatures: data.field_features_export.map(
                ({ icon, name }) => ({
                  icon: GetServerImageUrl(icon),
                  name,
                }),
              ),
              isMaintainance: false,
            }) as IPromoData,
        ),
      )
      .subscribe((v) => this.behav.next(v));
  }
}
