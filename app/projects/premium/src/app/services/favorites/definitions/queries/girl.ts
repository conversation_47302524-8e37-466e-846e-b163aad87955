import { gql } from 'apollo-angular';
import { IPreview } from 'projects/premium/src/app/models/preview';
import {
  PublicImage,
  PublicImageDerivative,
} from '../../../../screens/model/screens/model/definitions/models';
import { getFocalPointByImageIndex } from '../../../../utils/getFocalPointByImageIndex';

export function MapFavGirlResultToPreviews(data: {
  results: IFavGirlResult[];
}): IPreview[] {
  return data.results.map((v): IPreview => {
    const girlInfo =
      v.reverseGalleriesGirlInfo.entities[0]?.queryGirl.entities[0];
    const girlInfoGallery = v.reverseGalleriesGirlInfo.entities[0];
    const paywallImages =
      girlInfoGallery.fieldPublicImagesLow.map((img, index) => ({
        src: img.derivative.url,
        focalPoint: getFocalPointByImageIndex(girlInfoGallery, index),
      })) ||
      girlInfoGallery.fieldPublicImages.map((img, index) => ({
        src: img.url,
        focalPoint: getFocalPointByImageIndex(girlInfoGallery, index),
      })) ||
      [];
    return {
      id: v.fieldImage?.entity.entityId,
      girlId: girlInfo?.entityId,
      // girlInfoId: girlInfo?.entityId, // hide: otherwise will show wrong favorite type
      // girlId: v.entityId,
      publicImage: girlInfoGallery.fieldPublicImages?.[0]?.url,
      fieldPlusAccess: (girlInfo as any)?.fieldPlusAccess,
      paywallImages,
      text: girlInfo?.entityLabel,
      title: girlInfo?.entityLabel,
      image: v.fieldImage?.entity.fieldMediaImage.url,
      focalPoint: v.fieldImage?.entity
        ? {
            x: v.fieldImage.entity.fieldFocalPointX,
            y: v.fieldImage.entity.fieldFocalPointY,
          }
        : undefined,
      link: ['/girl', girlInfo?.entityId],
    };
  });
}

export interface IFavGirlResult {
  entityId: number;
  fieldImage: {
    entity: {
      entityId: number;
      fieldMediaImage: {
        url: string;
      };
      fieldFocalPointX: number;
      fieldFocalPointY: number;
    };
  };
  reverseGalleriesGirlInfo: {
    entities: {
      fieldMainFocalPointX?: number;
      fieldMainFocalPointY?: number;
      fieldSecondFocalPointX?: number;
      fieldSecondFocalPointY?: number;
      fieldThirdFocalPointX?: number;
      fieldThirdFocalPointY?: number;
      fieldPublicImages: PublicImage[];
      fieldPublicImagesLow: PublicImageDerivative[];
      queryGirl: {
        entities: {
          entityId: number;
          entityLabel: string;
        }[];
      };
    }[];
  };
}

export const GET_FAV_GIRLS = gql`
  query GetFavoriteGirls($pageSize: Int!, $page: Int!) {
    pbFavoritesGirls(pageSize: $pageSize, page: $page) {
      results {
        entityId
        fieldImage {
          entity {
            entityId
            ... on MediaImage {
              fieldFocalPointX
              fieldFocalPointY
              fieldMediaImage {
                url
              }
            }
          }
        }
        reverseGalleriesGirlInfo {
          entities {
            ... on GirlInfo {
              fieldMainFocalPointX
              fieldMainFocalPointY
              fieldSecondFocalPointX
              fieldSecondFocalPointY
              fieldThirdFocalPointX
              fieldThirdFocalPointY
              fieldPublicImages {
                url
              }
              fieldPublicImagesLow: fieldPublicImages {
                derivative(style: XLARGE) {
                  url
                }
              }
              queryGirl {
                entities {
                  entityId
                  entityLabel
                }
              }
            }
          }
        }
      }
    }
  }
`;
export const GET_FAV_GIRLS_META = gql`
  {
    pbFavoritesGirls {
      count
    }
  }
`;
