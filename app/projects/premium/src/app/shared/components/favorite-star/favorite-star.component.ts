import {
  animate,
  keyframes,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  Component,
  computed,
  HostBinding,
  HostListener,
  inject,
  input,
  Input,
  OnDestroy,
} from '@angular/core';
import { Subscription } from 'rxjs';

import {
  FavoritesService,
  FavoriteType,
} from '../../../services/favorites/favorites.service';
import { NgClass } from '@angular/common';
import { AccountService } from '../../../services/account.service';
import { SubscriptionPopupService } from '../../../components/subscription-popup/subscription-popup.service';
import { PaywallImage } from '../../../screens/model/screens/model/definitions/models';

@Component({
  selector: 'app-favorite-star',
  template: `
    <img
      class="h-8 transition-opacity"
      [@isFavAnim]="interactionLocked ? null : isFav"
      [class.hover:opacity-50]="isFav"
      [ngClass]="{
        'opacity-25 hover:opacity-75': !isFav && !interactionLocked,
      }"
      [src]="'assets/gallery/Star' + (!isFav ? '' : '-Yellow') + '.svg'"
    />

    <ng-content></ng-content>
  `,
  styleUrls: ['./favorite-star.component.css'],
  animations: [
    trigger('isFavAnim', [
      transition('* => *', [
        animate(
          '250ms ease-out',
          keyframes([
            style({ transform: 'scale(1) translateY(0)' }),
            style({ transform: 'scale(1.2) translateY(-0.1rem)' }),
            style({ transform: 'scale(1) translateY(0)' }),
          ]),
        ),
      ]),
    ]),
  ],
  imports: [NgClass],
})
export class FavoriteStarComponent implements OnDestroy {
  private readonly accountService = inject(AccountService);
  private readonly subscriptionType = this.accountService.subscriptionType;
  private readonly subscriptionPopupService: SubscriptionPopupService = inject(
    SubscriptionPopupService,
  );

  private _id: string;
  @Input() set id(id: string) {
    this._id = id;
    this.bindSubscription();
  }

  private _type: FavoriteType;
  @Input() set type(type: FavoriteType) {
    this._type = type;
    this.bindSubscription();
  }

  public isFav = false;

  @Input() tooltipText = '';

  @HostBinding('attr.title')
  get tooltip(): string | null {
    return this.tooltipText || null;
  }

  @Input() interactionLocked = false;

  private sub?: Subscription;

  paywallImages = input<Array<PaywallImage>>();

  @HostListener('click', ['$event'])
  private click(e: MouseEvent) {
    if (this.interactionLocked || !this._id || !this._type) {
      return;
    }
    if (this.subscriptionType() !== 'all-access' && this.subscriptionType() !== 'plus') {
      e.preventDefault();
      e.stopPropagation();
      this.subscriptionPopupService.open(false, {
        images: this.paywallImages(),
      });
      return false;
    } else {
      e.preventDefault();
      e.stopPropagation();
      this.favService.toggleFavorite(this._type, this._id);
      return false;
    }
  }

  private bindSubscription() {
    this.sub?.unsubscribe();
    if (!this._id || !this._type) {
      return;
    }
    this.sub = this.favService
      .getIsFavorite(this._id, this._type)
      .subscribe((fav) => (this.isFav = fav));
  }

  constructor(private readonly favService: FavoritesService) {}

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }
}
