import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  Input,
  HostBinding,
} from '@angular/core';
import { IPreview } from '../../../models/preview';
import {
  NexxComponent,
  PreloadImageComponent,
  PreviewVideoComponent,
} from '@pb/ui';
import { NgClass, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-article-preview',
  templateUrl: './article-preview.component.html',
  styleUrls: ['./article-preview.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    PreloadImageComponent,
    NgClass,
    PreviewVideoComponent,
    NexxComponent,
    NgTemplateOutlet,
  ],
})
export class ArticlePreviewComponent implements OnInit {
  @Input() image: string;
  @Input() imageRatio?: number;
  @Input() imageLowRes?: string;
  @Input() focalPoint?: { x: number; y: number };
  @Input() nexxID?: string;
  @Input() inlineVideo?: boolean;
  @Input() meta?: {
    images: number;
    videos: number;
    girlInfos: number;
  };

  @Input() new = false;
  @Input() tag?: string;

  @Input() withoutHoverEffect = false;

  @HostBinding('class.auto-size')
  @Input()
  autoSize = false;

  @Input() set previewData(data: IPreview) {
    this.image = data.image;
    this.imageLowRes = data.imageLowRes;
    this.focalPoint = data.focalPoint;
    this.nexxID = data.nexxID;
    this.new = data.isNew;
    this.meta = data.meta;
    this.imageRatio = data.imageRatio;
  }

  get metaDataTagText(): string {
    const { girlInfos, images, videos } = this.meta || {};

    const imagesText = images ? `<span class="whitespace-nowrap">${images} Bild${images > 1 ? 'er' : ''}</span>` : '';
    const videosText = videos ? `<span class="whitespace-nowrap">${videos} Video${videos > 1 ? 's' : ''}</span>` : '';
    const girlInfosText = girlInfos && girlInfos > 1 ? `<span class="whitespace-nowrap">${girlInfos} Galerien</span>` : '';

    const separator1 =
      girlInfos && girlInfos > 1 && (images || videos) ? ' · ' : '';
    const separator2 = images && videos ? ' · ' : '';

    return `${girlInfosText}${separator1}${imagesText}${separator2}${videosText}`.trim();
  }

  constructor() {}

  ngOnInit(): void {}
}
