import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { map, Observable } from 'rxjs';

import { AccountService } from '../services/account.service';

@Injectable({
  providedIn: 'root',
})
export class LoggedInGuard {
  constructor(
    private acc: AccountService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    // get logged status
    return this.acc.Subscribed.pipe(
      map((subscribed) => {
        // if not logged, check where to redirect
        if (!subscribed) {
          let url = ['/'];
          let queryParamsObject: Record<string, string> | null = null;

          // if girl or girl info was requested, go to public girl
          if (route.routeConfig?.path == 'girl') {
            const params = state.url.split('/');
            if (params.length === 4) {
              url = [`/p/girl/info/${params[3]}`];
            } else if (params.length === 3) {
              url = [`/p/girl/${params[2]}`];
            }
          } else if (route.routeConfig?.path == 'categories') {
            const params = state.url.split('/');
            if (params.length === 3) {
              const category = decodeURIComponent(params[2]);
              url = [`/p/categories/${category}`];
            }
          } else if (route.routeConfig?.path == 'search') {
            queryParamsObject = Object.fromEntries(
              new URLSearchParams(state.url.split('?')[1]),
            );
            url = [`/p/search`];
          } else if (route.routeConfig?.path == ':modular_url') {
            const params = state.url.split('/');
            url = [`/p/${params[1]}`];
          }

          return this.router.createUrlTree(url, {
            queryParams: queryParamsObject,
          });
        }
        return subscribed;
      }),
    );
  }
}
