import {
  Component,
  OnInit,
} from '@angular/core';
import { Meta } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ModularComponent } from '../../modular/modular.component';
import { UtmService } from '../../../services/utm.service';

declare var upScore: any;
@Component({
  selector: 'app-landing-subscribed',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.css'],
  imports: [
    ReactiveFormsModule,
    FormsModule,
    ModularComponent,
  ],
})
export class LandingSubscribedComponent implements OnInit {

  constructor(
    private meta: Meta,
    private utmService: UtmService,
  ) {}

  ngOnInit(): void {
    if (typeof upScore !== 'undefined') {
      const upScoreObjectId = 'aa_6';
      let upScoreConfig = {};
      upScoreConfig = {
        config: {
          domain: 'premium.playboy.de',
          article: '',
          track_positions: false,
        },
        data: {
          section: 'homepage',
          taxonomy: '',
          object_id: upScoreObjectId,
          pubdate: '2021-10-10T20:15:00+2:00',
          author: '',
          object_type: 'homepage',
          custom_source: '',
          custom_app: 0,
          custom_video: 0,
          custom_audio: 0,
          content_type: 1,
          content_blocked: 1,
          conversion: 2,
          user_status: 2,
        },
      };
      console.log('upScore Config:', upScoreConfig);

      upScore(upScoreConfig);
      this.utmService.handleUTM(upScoreObjectId);
    }
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);
    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({ name: 'og:title', content: 'Home' });
    this.meta.addTag({
      name: 'og:description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({
      name: 'description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    this.meta.addTag({
      name: 'og:url',
      content: 'https://premium.playboy.de/',
    });
  }

}
