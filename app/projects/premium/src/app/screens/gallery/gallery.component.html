@if (settings) {
  @if ($girlInfo | async; as girlInfo) {
    <lightgallery
      [settings]="settings"
      [onInit]="onInit"
      [onAfterSlide]="onAfterSlide"
    >
      @for (item of $items | async; track item; let i = $index) {
        <a
          class="gallery-item"
          [attr.data-src]="
            item?.entity?.fieldVideoHash
              ? 'https://embed.nexx.cloud/12025/video/' +
                item?.entity?.fieldVideoHash
              : (item?.entity?.fieldMediaImage?.url | cdn)
          "
          [attr.data-sub-html]="'.caption-' + i"
          [attr.data-iframe]="!!item?.entity?.fieldVideoHash || null"
        >
          <img
            [src]="
              (
                item?.entity?.fieldMediaImage ||
                item?.entity?.fieldMediaImage?.derivative ||
                item?.entity?.fieldPreviewImage?.entity?.fieldMediaImage
              )?.url | cdn
            "
            style="display: none"
          />
          <!-- [class]="item?.entity?.fieldVideoHash ? 'testprev' : '' " -->
        </a>
      }
    </lightgallery>
  }
}

@if ($item | async; as item) {
  <!--  Only show favorite star for videos -->
  @if (item.entity?.fieldVideoHash || item.entity?.fieldNexxId) {
    <app-favorite-star
      class="lg-star"
      [style.zIndex]="9999"
      [type]="
        item.entity?.fieldVideoHash || item.entity?.fieldNexxId
          ? 'video'
          : 'image'
      "
      [id]="$mid | async"
    >
    </app-favorite-star>
  }
}

@if ($girlInfo | async; as girlInfo) {
  <div class="lg-closeBtn">
    <a
      [routerLink]="[
        '/girl',
        girlInfo?.girlInfoById?.queryGirl?.entities[0].id,
        $girlInfoId | async,
      ]"
      (click)="checkReturnUrl()"
      [queryParams]="{ mid: item?.entity?.mid }"
    >
      <img src="assets/gallery/Close.svg" />
    </a>
  </div>
}
@if (settings) {
  @if ($girlInfo | async; as girlInfo) {
    @for (item of $items | async; track item; let i = $index) {
      <div class="caption caption-{{ i }} flex justify-center absolute top-0">
<!--        @if (item) {-->
<!--          <div-->
<!--            [innerHTML]="-->
<!--              item.fieldDescription?.processed ||-->
<!--              item.alt ||-->
<!--              girlInfo?.queryGirl?.entities[0].name-->
<!--            "-->
<!--            class="mr-1"-->
<!--          >-->
<!--            &lt;!&ndash; {{girlInfo.description || girlInfo.queryGirl.entities[0].name}} &ndash;&gt;-->
<!--          </div>-->
<!--        }-->
        @if (item?.entity?.fieldCredit; as credit) {
          Credit: {{ credit }}
        } @else {
          @if (
            girlInfo?.girlInfoById?.queryGalleries?.entities[0]?.fieldCredit;
            as credit
          ) {
            Credit: {{ credit }}
          }
        }
      </div>
    }
  }
}
