<h2 class="my-6 md:my-20 w-full text-center p-4">Suche</h2>

@if (loading) {
  <div
    class="bg-black bg-opacity-25 h-screen left-0 fixed top-0 w-screen z-40 flex justify-center items-center pointer-events-none"
  >
    <img src="assets/bunnyanimation-white.gif" alt="Loading..." class="w-20" />
  </div>
}

<form
  [formGroup]="formGroup"
  class="flex flex-col items-center relative mx-auto justify-center"
  id="search-container"
>
  <div class="w-full relative mb-6 md:mb-10 z-30">
    <app-search-input
      [autofocus]="true"
      formControlName="search"
      placeholder="Suche"
    ></app-search-input>
  </div>
</form>

<!--<section class="container">-->
<!--  <lib-breadcrumbs-->
<!--    [breadcrumbs]="[{ link: '/search', label: 'Such<PERSON>' }]"-->
<!--    class="mt-8 md:mt-0"-->
<!--  >-->
<!--  </lib-breadcrumbs>-->
<!--</section>-->

@if (!!term && (!models || models.length <= 0)) {
  <div class="p-4 text-center">
    Keine Ergebnisse für <b>{{ term }}</b> gefunden
  </div>
} @else if (!!term) {
  <h3 class="p-4 w-full text-center mb-6 md:mb-10" id="title-proposal">
    Ergebnisse für "{{ term }}"
  </h3>
}

@if (!!term) {
  <ng-template
    #article
    let-id="id"
    let-link="link"
    let-model="item"
    let-image="image"
  >
    <a class="w-full flex cursor-pointer h-full max-h-full" [routerLink]="link">
      <app-gallery-card
        class="w-full"
        [previewData]="model"
        [image]="image"
      ></app-gallery-card>
    </a>
  </ng-template>

  <app-pagination-grid
    [paginationAnimationElementSelector]="'#search-container'"
    [items]="models"
    [page]="$page | async"
    [pages]="$pageAmount | async"
    [loading]="loading"
    [template]="article"
  ></app-pagination-grid>
}
