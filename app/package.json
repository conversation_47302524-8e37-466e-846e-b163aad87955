{"name": "playboy-premium", "version": "0.0.1", "description": "Premium ;)", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build:prod": "ng build --configuration production", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "generate-routes": "node scripts/generate-routes.js", "build-and-prerender": "npm run generate-routes && npm run build:ssr && npm run prerender", "serve:ssr:premium": "npm run build:prod && export PORT=4200 && node dist/premium/server/server.mjs", "prettier:write": "prettier . --write", "prettier:check": "prettier . --check"}, "private": true, "dependencies": {"@angular/animations": "^19.1.3", "@angular/common": "^19.1.3", "@angular/compiler": "^19.1.3", "@angular/core": "^19.1.3", "@angular/forms": "^19.1.3", "@angular/platform-browser": "^19.1.3", "@angular/platform-browser-dynamic": "^19.1.3", "@angular/platform-server": "^19.1.3", "@angular/router": "^19.1.3", "@angular/ssr": "^19.1.4", "@apollo/client": "3.12.7", "apollo-angular": "8.0.0", "express": "^4.18.2", "graphql": "16.10.0", "hammerjs": "^2.0.8", "lightgallery": "^2.6.1", "ngx-cookie-service-ssr": "^19.0.0", "node-fetch": "^2.7.0", "rxjs": "7.8.1", "tslib": "^2.0.0", "zone.js": "~0.15.0", "@angular/cdk": "^19.1.3"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.4", "@angular/cli": "^19.1.4", "@angular/compiler-cli": "^19.1.3", "@types/express": "^4.17.17", "@types/googlemaps": "^3.43.2", "@types/hammerjs": "^2.0.40", "@types/jasmine": "~3.8.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^18.18.0", "angular-in-memory-web-api": "^0.11.0", "browser-sync": "^3.0.0", "codelyzer": "^6.0.0", "fullscreen-api-polyfill": "^1.1.2", "iban": "0.0.14", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "moment": "^2.29.1", "ng-click-outside2": "15.0.1", "ng-packagr": "^19.1.1", "protractor": "~7.0.0", "swiper": "11.2.1", "tailwindcss": "^2.2.4", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~5.7.3", "prettier": "3.5.3"}}